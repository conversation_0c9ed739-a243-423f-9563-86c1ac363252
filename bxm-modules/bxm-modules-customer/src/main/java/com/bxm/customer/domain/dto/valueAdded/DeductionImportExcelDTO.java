package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * 扣款导入Excel DTO
 *
 * 用于扣款操作的Excel导入，继承基类的公共字段，并添加扣款操作特有字段：
 * 交付结果
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@ApiModel("扣款导入Excel DTO")
public class DeductionImportExcelDTO extends BaseImportExcelDTO {

    /** 交付结果 */
    @Excel(name = "交付结果", sort = 4)
    @NotBlank(message = "交付结果不能为空")
    @ApiModelProperty(value = "交付结果", required = true)
    private String deliveryResult;
}
