package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;

/**
 * 增值交付单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Mapper
public interface ValueAddedDeliveryOrderMapper extends BaseMapper<ValueAddedDeliveryOrder>
{
    /**
     * 查询增值交付单
     * 
     * @param id 增值交付单主键
     * @return 增值交付单
     */
    public ValueAddedDeliveryOrder selectValueAddedDeliveryOrderById(Long id);

    /**
     * 查询增值交付单列表
     * 
     * @param valueAddedDeliveryOrder 增值交付单
     * @return 增值交付单集合
     */
    public List<ValueAddedDeliveryOrder> selectValueAddedDeliveryOrderList(ValueAddedDeliveryOrder valueAddedDeliveryOrder);

    /**
     * 新增增值交付单
     * 
     * @param valueAddedDeliveryOrder 增值交付单
     * @return 结果
     */
    public int insertValueAddedDeliveryOrder(ValueAddedDeliveryOrder valueAddedDeliveryOrder);

    /**
     * 修改增值交付单
     * 
     * @param valueAddedDeliveryOrder 增值交付单
     * @return 结果
     */
    public int updateValueAddedDeliveryOrder(ValueAddedDeliveryOrder valueAddedDeliveryOrder);

    /**
     * 删除增值交付单
     * 
     * @param id 增值交付单主键
     * @return 结果
     */
    public int deleteValueAddedDeliveryOrderById(Long id);

    /**
     * 批量删除增值交付单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteValueAddedDeliveryOrderByIds(Long[] ids);
}
