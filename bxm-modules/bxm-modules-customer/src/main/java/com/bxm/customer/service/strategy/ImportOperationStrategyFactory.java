package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导入操作策略工厂
 *
 * 负责管理和提供不同操作类型的导入策略实例
 * 支持的操作类型：
 * 1. DELIVERY - 交付操作
 * 2. SUPPLEMENT_DELIVERY - 补充交付附件
 * 3. DEDUCTION - 扣款操作
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class ImportOperationStrategyFactory {

    @Autowired
    private List<ImportOperationStrategy> strategies;

    private final Map<ValueAddedBatchImportOperationType, ImportOperationStrategy> strategyMap = new HashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        for (ImportOperationStrategy strategy : strategies) {
            ValueAddedBatchImportOperationType operationType = strategy.getSupportedOperationType();
            if (operationType != null) {
                strategyMap.put(operationType, strategy);
                log.info("Registered import operation strategy: {} -> {}",
                        operationType.getDescription(), strategy.getClass().getSimpleName());
            }
        }
        log.info("Total {} import operation strategies registered", strategyMap.size());
    }

    /**
     * 根据操作类型获取对应的策略
     *
     * @param operationType 操作类型
     * @return 对应的策略实例
     * @throws IllegalArgumentException 当操作类型不支持时抛出
     */
    public ImportOperationStrategy getStrategy(ValueAddedBatchImportOperationType operationType) {
        if (operationType == null) {
            throw new IllegalArgumentException("Operation type cannot be null");
        }

        ImportOperationStrategy strategy = strategyMap.get(operationType);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported operation type: " + operationType.getDescription());
        }

        return strategy;
    }

    /**
     * 获取所有支持的操作类型
     *
     * @return 支持的操作类型列表
     */
    public ValueAddedBatchImportOperationType[] getSupportedOperationTypes() {
        return strategyMap.keySet().toArray(new ValueAddedBatchImportOperationType[0]);
    }

    /**
     * 检查操作类型是否支持
     *
     * @param operationType 操作类型
     * @return 是否支持
     */
    public boolean isSupported(ValueAddedBatchImportOperationType operationType) {
        return operationType != null && strategyMap.containsKey(operationType);
    }

}
