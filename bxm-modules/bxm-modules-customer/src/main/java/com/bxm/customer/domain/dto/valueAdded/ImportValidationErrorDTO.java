package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入校验错误DTO
 *
 * 用于记录Excel导入过程中的校验错误信息
 * 包含行号、交付单编号、错误信息等详细信息
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("导入校验错误DTO")
public class ImportValidationErrorDTO {

    /**
     * Excel行号
     */
    @Excel(name = "行号")
    @ApiModelProperty(value = "Excel行号")
    private Integer rowNumber;

    /**
     * 交付单编号
     */
    @Excel(name = "交付单编号")
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /**
     * 企业名
     */
    @Excel(name = "企业名")
    @ApiModelProperty(value = "企业名")
    private String customerName;

    /**
     * 信用代码
     */
    @Excel(name = "信用代码")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 错误类型
     */
    @Excel(name = "错误类型")
    @ApiModelProperty(value = "错误类型")
    private String errorType;

    /**
     * 创建校验错误的静态方法
     */
    public static ImportValidationErrorDTO create(Integer rowNumber, String deliveryOrderNo,
                                                String customerName, String creditCode,
                                                String errorMessage, String errorType) {
        return ImportValidationErrorDTO.builder()
                .rowNumber(rowNumber)
                .deliveryOrderNo(deliveryOrderNo)
                .customerName(customerName)
                .creditCode(creditCode)
                .errorMessage(errorMessage)
                .errorType(errorType)
                .build();
    }

    /**
     * 创建简单校验错误的静态方法
     */
    public static ImportValidationErrorDTO create(Integer rowNumber, String deliveryOrderNo, String errorMessage) {
        return ImportValidationErrorDTO.builder()
                .rowNumber(rowNumber)
                .deliveryOrderNo(deliveryOrderNo)
                .errorMessage(errorMessage)
                .errorType("校验错误")
                .build();
    }
}
