package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 补充交付附件导入Excel DTO
 *
 * 用于补充交付附件操作的Excel导入，继承基类的所有字段，无额外特有字段
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@ApiModel("补充交付附件导入Excel DTO")
public class SupplementDeliveryImportExcelDTO extends BaseImportExcelDTO {
    // 无额外字段，仅继承基类字段
}
