package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerTaxTypeCheck;

/**
 * 新户流转税种核定Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerTaxTypeCheckMapper extends BaseMapper<NewCustomerTaxTypeCheck>
{
    /**
     * 查询新户流转税种核定
     * 
     * @param id 新户流转税种核定主键
     * @return 新户流转税种核定
     */
    public NewCustomerTaxTypeCheck selectNewCustomerTaxTypeCheckById(Long id);

    /**
     * 查询新户流转税种核定列表
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 新户流转税种核定集合
     */
    public List<NewCustomerTaxTypeCheck> selectNewCustomerTaxTypeCheckList(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck);

    /**
     * 新增新户流转税种核定
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 结果
     */
    public int insertNewCustomerTaxTypeCheck(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck);

    /**
     * 修改新户流转税种核定
     * 
     * @param newCustomerTaxTypeCheck 新户流转税种核定
     * @return 结果
     */
    public int updateNewCustomerTaxTypeCheck(NewCustomerTaxTypeCheck newCustomerTaxTypeCheck);

    /**
     * 删除新户流转税种核定
     * 
     * @param id 新户流转税种核定主键
     * @return 结果
     */
    public int deleteNewCustomerTaxTypeCheckById(Long id);

    /**
     * 批量删除新户流转税种核定
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerTaxTypeCheckByIds(Long[] ids);
}
