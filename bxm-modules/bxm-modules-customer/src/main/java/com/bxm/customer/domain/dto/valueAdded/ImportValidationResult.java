package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedItemType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 导入校验结果DTO
 *
 * 封装批量校验的结果信息，包括：
 * 1. 基础校验结果（表格格式、唯一性等）
 * 2. 业务校验结果（交付单存在性、前置条件等）
 * 3. 批量查询的数据（交付单、增值事项类型）
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("导入校验结果DTO")
public class ImportValidationResult {

    /**
     * 校验是否通过
     */
    @ApiModelProperty(value = "校验是否通过")
    private Boolean isValid;

    /**
     * 校验错误列表
     */
    @ApiModelProperty(value = "校验错误列表")
    private List<ImportValidationErrorDTO> errors;

    /**
     * 有效的数据列表（通过校验的）
     */
    @ApiModelProperty(value = "有效的数据列表")
    private List<? extends BaseImportExcelDTO> validData;

    /**
     * 交付单编号到交付单对象的映射
     * key: 交付单编号, value: 交付单对象
     */
    @ApiModelProperty(value = "交付单编号到交付单对象的映射")
    private Map<String, ValueAddedDeliveryOrder> deliveryOrderMap;

    /**
     * 增值事项类型ID到增值事项类型对象的映射
     * key: 增值事项类型ID, value: 增值事项类型对象
     */
    @ApiModelProperty(value = "增值事项类型ID到增值事项类型对象的映射")
    private Map<Integer, ValueAddedItemType> itemTypeMap;

    /**
     * 创建成功的校验结果
     *
     * @param validData 有效数据列表
     * @param deliveryOrderMap 交付单映射
     * @param itemTypeMap 增值事项类型映射
     * @return 校验结果
     */
    public static ImportValidationResult success(List<? extends BaseImportExcelDTO> validData,
                                                Map<String, ValueAddedDeliveryOrder> deliveryOrderMap,
                                                Map<Integer, ValueAddedItemType> itemTypeMap) {
        return ImportValidationResult.builder()
                .isValid(true)
                .validData(validData)
                .deliveryOrderMap(deliveryOrderMap)
                .itemTypeMap(itemTypeMap)
                .build();
    }

    /**
     * 创建失败的校验结果
     *
     * @param errors 错误列表
     * @return 校验结果
     */
    public static ImportValidationResult failure(List<ImportValidationErrorDTO> errors) {
        return ImportValidationResult.builder()
                .isValid(false)
                .errors(errors)
                .build();
    }

    /**
     * 创建部分成功的校验结果
     *
     * @param validData 有效数据列表
     * @param errors 错误列表
     * @param deliveryOrderMap 交付单映射
     * @param itemTypeMap 增值事项类型映射
     * @return 校验结果
     */
    public static ImportValidationResult partial(List<? extends BaseImportExcelDTO> validData,
                                                List<ImportValidationErrorDTO> errors,
                                                Map<String, ValueAddedDeliveryOrder> deliveryOrderMap,
                                                Map<Integer, ValueAddedItemType> itemTypeMap) {
        return ImportValidationResult.builder()
                .isValid(validData != null && !validData.isEmpty())
                .validData(validData)
                .errors(errors)
                .deliveryOrderMap(deliveryOrderMap)
                .itemTypeMap(itemTypeMap)
                .build();
    }
}
