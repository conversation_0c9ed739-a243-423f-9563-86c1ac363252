package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.api.domain.dto.RemoteCustomerBankAccountDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerBankAccount;
import org.apache.ibatis.annotations.Param;

/**
 * 新户流转银行账号Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerBankAccountMapper extends BaseMapper<NewCustomerBankAccount>
{
    /**
     * 查询新户流转银行账号
     * 
     * @param id 新户流转银行账号主键
     * @return 新户流转银行账号
     */
    public NewCustomerBankAccount selectNewCustomerBankAccountById(Long id);

    /**
     * 查询新户流转银行账号列表
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 新户流转银行账号集合
     */
    public List<NewCustomerBankAccount> selectNewCustomerBankAccountList(NewCustomerBankAccount newCustomerBankAccount);

    /**
     * 新增新户流转银行账号
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 结果
     */
    public int insertNewCustomerBankAccount(NewCustomerBankAccount newCustomerBankAccount);

    /**
     * 修改新户流转银行账号
     * 
     * @param newCustomerBankAccount 新户流转银行账号
     * @return 结果
     */
    public int updateNewCustomerBankAccount(NewCustomerBankAccount newCustomerBankAccount);

    /**
     * 删除新户流转银行账号
     * 
     * @param id 新户流转银行账号主键
     * @return 结果
     */
    public int deleteNewCustomerBankAccountById(Long id);

    /**
     * 批量删除新户流转银行账号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerBankAccountByIds(Long[] ids);

    Integer getBusinessTopDeptBankAccountCount(@Param("businessTopDeptId") Long businessTopDeptId, @Param("account") String account, @Param("id") Long id, @Param("creditCode") String creditCode);

    RemoteCustomerBankAccountDTO getNewCustomerBankAccountNumberByBankNumberAndBusinessTopDeptId(@Param("bankAccountNumber") String bankAccountNumber,
                                                                                                 @Param("businessTopDeptId") Long businessTopDeptId);
}
