package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.QualityCheckingRecord;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.qualityChecking.QualityCheckingRecordDTO;
import com.bxm.customer.domain.vo.qualityChecking.QualityCheckingRecordVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 质检记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Mapper
public interface QualityCheckingRecordMapper extends BaseMapper<QualityCheckingRecord>
{
    /**
     * 查询质检记录
     * 
     * @param id 质检记录主键
     * @return 质检记录
     */
    public QualityCheckingRecord selectQualityCheckingRecordById(Long id);

    /**
     * 查询质检记录列表
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 质检记录集合
     */
    public List<QualityCheckingRecord> selectQualityCheckingRecordList(QualityCheckingRecord qualityCheckingRecord);

    /**
     * 新增质检记录
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 结果
     */
    public int insertQualityCheckingRecord(QualityCheckingRecord qualityCheckingRecord);

    /**
     * 修改质检记录
     * 
     * @param qualityCheckingRecord 质检记录
     * @return 结果
     */
    public int updateQualityCheckingRecord(QualityCheckingRecord qualityCheckingRecord);

    /**
     * 删除质检记录
     * 
     * @param id 质检记录主键
     * @return 结果
     */
    public int deleteQualityCheckingRecordById(Long id);

    /**
     * 批量删除质检记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQualityCheckingRecordByIds(Long[] ids);

    List<CommonDeptCountDTO> qualityCheckingRecordAdvisorDeptList(@Param("userDept") UserDeptDTO userDeptDTO, @Param("vo") QualityCheckingRecordVO vo);

    List<CommonDeptCountDTO> qualityCheckingRecordAccountingDeptList(@Param("userDept") UserDeptDTO userDeptDTO, @Param("vo") QualityCheckingRecordVO vo);

    List<QualityCheckingRecordDTO> qualityCheckingRecordListByIds(@Param("ids") List<Long> ids);

    List<QualityCheckingRecordDTO> qualityCheckingRecordPageList(IPage<QualityCheckingRecordDTO> result,
                                                                 @Param("vo") QualityCheckingRecordVO vo,
                                                                 @Param("userDept") UserDeptDTO userDept,
                                                                 @Param("batchSearchCustomerServiceIds") List<Long> batchSearchCustomerServiceIds);
}
