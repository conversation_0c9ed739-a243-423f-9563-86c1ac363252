package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 交付导入Excel DTO
 *
 * 用于交付操作的Excel导入，继承基类的公共字段，并添加交付操作特有字段：
 * 交付结果、总扣缴金额
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@ApiModel("交付导入Excel DTO")
public class DeliveryImportExcelDTO extends BaseImportExcelDTO {

    /** 交付结果 */
    @Excel(name = "交付结果", sort = 4)
    @NotBlank(message = "交付结果不能为空")
    @ApiModelProperty(value = "交付结果", required = true)
    private String deliveryResult;

    /** 总扣缴金额 */
    @Excel(name = "总扣缴金额", sort = 5)
    @ApiModelProperty(value = "总扣缴金额（有条件必填）")
    private BigDecimal totalWithholdingAmount;
}
